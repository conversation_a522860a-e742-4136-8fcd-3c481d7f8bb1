import mongoose from 'mongoose';

const serviceSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان الخدمة مطلوب'],
    trim: true,
    maxlength: [200, 'العنوان يجب أن يكون أقل من 200 حرف']
  },
  slug: {
    type: String,
    required: [true, 'الرابط المختصر مطلوب'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطات فقط']
  },
  excerpt: {
    type: String,
    required: [true, 'ملخص الخدمة مطلوب'],
    trim: true,
    maxlength: [300, 'الملخص يجب أن يكون أقل من 300 حرف']
  },
  description: {
    type: String,
    required: [true, 'وصف الخدمة مطلوب'],
    trim: true,
    maxlength: [500, 'الوصف يجب أن يكون أقل من 500 حرف']
  },
  content: {
    type: String,
    required: [true, 'محتوى الخدمة مطلوب']
  },
  image: {
    type: String,
    trim: true
  },
  coverImage: {
    type: String,
    trim: true
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'فئة الخدمة مطلوبة']
  },
  tags: [{
    type: String,
    trim: true
  }],
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'كاتب الخدمة مطلوب']
  },
  publishDate: {
    type: Date,
    default: Date.now
  },
  readTime: {
    type: Number,
    default: 5,
    min: 1,
    max: 60
  },
  relatedServices: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service'
  }],
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  featured: {
    type: Boolean,
    default: false
  },
  price: {
    type: String,
    trim: true
  },
  duration: {
    type: String,
    trim: true
  },
  requirements: {
    type: String,
    trim: true
  },
  benefits: [{
    type: String,
    trim: true
  }],
  process: [{
    step: {
      type: Number,
      required: true
    },
    title: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    }
  }],
  faqs: [{
    question: {
      type: String,
      required: true,
      trim: true
    },
    answer: {
      type: String,
      required: true,
      trim: true
    }
  }],
  views: {
    type: Number,
    default: 0
  },
  inquiries: {
    type: Number,
    default: 0
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  meta: {
    title: {
      type: String,
      trim: true,
      maxlength: [60, 'عنوان SEO يجب أن يكون أقل من 60 حرف']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [160, 'وصف SEO يجب أن يكون أقل من 160 حرف']
    },
    keywords: {
      type: String,
      trim: true
    }
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// Middleware لحساب وقت القراءة تلقائياً
serviceSchema.pre('save', function(next) {
  if (this.isModified('content')) {
    this.readTime = this.calculateReadTime();
  }
  next();
});

// فهرسة للبحث والأداء
serviceSchema.index({ slug: 1 }, { unique: true });
serviceSchema.index({ status: 1 });
serviceSchema.index({ category: 1 });
serviceSchema.index({ author: 1 });
serviceSchema.index({ featured: 1 });
serviceSchema.index({ publishDate: -1 });
serviceSchema.index({ created_at: -1 });
serviceSchema.index({ views: -1 });
serviceSchema.index({ tags: 1 });
serviceSchema.index({
  title: 'text',
  excerpt: 'text',
  description: 'text',
  content: 'text',
  tags: 'text'
}, {
  weights: {
    title: 10,
    excerpt: 8,
    description: 5,
    content: 1,
    tags: 3
  }
});

// طرق النموذج
serviceSchema.methods.incrementViews = function() {
  return this.updateOne({ $inc: { views: 1 } });
};

serviceSchema.methods.incrementInquiries = function() {
  return this.updateOne({ $inc: { inquiries: 1 } });
};

serviceSchema.methods.calculateReadTime = function() {
  const wordsPerMinute = 200; // متوسط سرعة القراءة
  const wordCount = this.content.split(/\s+/).length;
  const readTime = Math.ceil(wordCount / wordsPerMinute);
  return Math.max(1, readTime); // على الأقل دقيقة واحدة
};

serviceSchema.methods.getFormattedTags = function() {
  return this.tags.filter(tag => tag && tag.trim()).map(tag => tag.trim());
};

serviceSchema.methods.updateRating = function(newRating) {
  const currentAverage = this.rating.average;
  const currentCount = this.rating.count;
  
  const newCount = currentCount + 1;
  const newAverage = ((currentAverage * currentCount) + newRating) / newCount;
  
  return this.updateOne({
    'rating.average': Math.round(newAverage * 10) / 10,
    'rating.count': newCount
  });
};

// طرق ثابتة
serviceSchema.statics.findPublished = function(options = {}) {
  const { page = 1, limit = 10, sort = 'publishDate' } = options;
  const skip = (page - 1) * limit;

  return this.find({ status: 'published' })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ [sort]: -1 })
    .skip(skip)
    .limit(limit);
};

serviceSchema.statics.findFeatured = function(limit = 6) {
  return this.find({ status: 'published', featured: true })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ publishDate: -1 })
    .limit(limit);
};

serviceSchema.statics.findByCategory = function(categoryId, options = {}) {
  const { page = 1, limit = 10 } = options;
  const skip = (page - 1) * limit;

  return this.find({ status: 'published', category: categoryId })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ publishDate: -1 })
    .skip(skip)
    .limit(limit);
};

serviceSchema.statics.findByAuthor = function(authorId, options = {}) {
  const { page = 1, limit = 10 } = options;
  const skip = (page - 1) * limit;

  return this.find({ status: 'published', author: authorId })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ publishDate: -1 })
    .skip(skip)
    .limit(limit);
};

serviceSchema.statics.findByTags = function(tags, options = {}) {
  const { page = 1, limit = 10 } = options;
  const skip = (page - 1) * limit;

  return this.find({
    status: 'published',
    tags: { $in: Array.isArray(tags) ? tags : [tags] }
  })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ publishDate: -1 })
    .skip(skip)
    .limit(limit);
};

serviceSchema.statics.searchServices = function(query, options = {}) {
  const { page = 1, limit = 10, category, author, tags } = options;
  const skip = (page - 1) * limit;

  let searchQuery = {
    $text: { $search: query },
    status: 'published'
  };

  // إضافة فلاتر إضافية
  if (category) searchQuery.category = category;
  if (author) searchQuery.author = author;
  if (tags && tags.length > 0) searchQuery.tags = { $in: tags };

  return this.find(searchQuery, { score: { $meta: 'textScore' } })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ score: { $meta: 'textScore' } })
    .skip(skip)
    .limit(limit);
};

serviceSchema.statics.getRelatedServices = function(serviceId, categoryId, limit = 4) {
  return this.find({
    status: 'published',
    _id: { $ne: serviceId },
    category: categoryId
  })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ views: -1, publishDate: -1 })
    .limit(limit);
};

serviceSchema.statics.getLatestServices = function(limit = 6) {
  return this.find({ status: 'published' })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ publishDate: -1 })
    .limit(limit);
};

serviceSchema.statics.getTrendingServices = function(limit = 6) {
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  return this.find({
    status: 'published',
    publishDate: { $gte: oneWeekAgo }
  })
    .populate('category', 'name slug')
    .populate('author', 'username email')
    .sort({ views: -1, publishDate: -1 })
    .limit(limit);
};

serviceSchema.statics.getPopular = function(limit = 10) {
  return this.find({ status: 'published' })
    .sort({ views: -1, inquiries: -1 })
    .limit(limit);
};

serviceSchema.statics.getTopRated = function(limit = 10) {
  return this.find({ 
    status: 'published',
    'rating.count': { $gte: 1 }
  })
    .sort({ 'rating.average': -1, 'rating.count': -1 })
    .limit(limit);
};

// تصدير النموذج
const Service = mongoose.models.Service || mongoose.model('Service', serviceSchema);

export default Service;

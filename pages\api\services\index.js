import connectDB from '../../../lib/mongodb';
import Service from '../../../lib/models/Service';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    await connectDB();

    const {
      page = 1,
      limit = 10,
      search = '',
      category = '',
      tag = '',
      author = '',
      sort = 'latest',
      featured = ''
    } = req.query;

    // بناء query للبحث والفلترة
    let query = { status: 'published' };
    let sortOptions = {};

    // البحث النصي
    if (search) {
      query.$text = { $search: search };
    }

    // فلترة حسب التصنيف
    if (category) {
      query.category = category;
    }

    // فلترة حسب الوسوم
    if (tag) {
      query.tags = { $in: [tag] };
    }

    // فلترة حسب المؤلف
    if (author) {
      query.author = author;
    }

    // فلترة الخدمات المميزة
    if (featured === 'true') {
      query.featured = true;
    }

    // ترتيب النتائج
    switch (sort) {
      case 'popular':
        sortOptions = { views: -1, publishDate: -1 };
        break;
      case 'rating':
        sortOptions = { 'rating.average': -1, 'rating.count': -1 };
        break;
      case 'alphabetical':
        sortOptions = { title: 1 };
        break;
      case 'latest':
      default:
        sortOptions = { publishDate: -1, created_at: -1 };
        break;
    }

    // إذا كان هناك بحث نصي، أضف score للترتيب
    if (search) {
      sortOptions = { score: { $meta: 'textScore' }, ...sortOptions };
    }

    // جلب الخدمات مع التقسيم
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const services = await Service.find(query)
      .populate('category', 'name slug')
      .populate('author', 'username email')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .select('-__v');

    // حساب العدد الإجمالي
    const total = await Service.countDocuments(query);

    return res.status(200).json({
      success: true,
      services,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching services:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الخدمات'
    });
  }
}

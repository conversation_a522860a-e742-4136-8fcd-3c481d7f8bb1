import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import Layout from '../../components/Layout';
import { 
  FaSearch, 
  FaFilter, 
  FaClock, 
  FaEye, 
  FaUser, 
  FaCalendar,
  FaTags,
  FaArrowRight,
  FaGavel,
  FaPhone,
  FaEnvelope
} from 'react-icons/fa';

const ServicesPage = ({ services, categories, totalServices, currentPage, totalPages }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const [sortBy, setSortBy] = useState('latest');
  const [filteredServices, setFilteredServices] = useState(services);
  const [loading, setLoading] = useState(false);

  // تطبيق الفلاتر
  useEffect(() => {
    if (services) {
      setFilteredServices(services);
    }
  }, [services]);

  useEffect(() => {
    filterServices();
  }, [searchQuery, selectedCategory, selectedTag, sortBy]);

  const filterServices = async () => {
    if (!searchQuery && !selectedCategory && !selectedTag && sortBy === 'latest') {
      setFilteredServices(services);
      return;
    }

    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (selectedCategory) params.append('category', selectedCategory);
      if (selectedTag) params.append('tag', selectedTag);
      if (sortBy) params.append('sort', sortBy);

      const response = await fetch(`/api/services?${params.toString()}`);
      const data = await response.json();
      
      if (data.success) {
        setFilteredServices(data.services);
      }
    } catch (error) {
      console.error('Error filtering services:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <Head>
        <title>الخدمات القانونية - مكتب المحاماة</title>
        <meta name="description" content="تصفح مجموعة شاملة من الخدمات القانونية المتخصصة التي نقدمها في مختلف مجالات القانون" />
        <meta name="keywords" content="خدمات قانونية, استشارات قانونية, محاماة, قانون" />
      </Head>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-primary-900 via-primary-800 to-primary-700 py-20">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="relative container mx-auto px-4 text-center text-white">
          <div className="max-w-4xl mx-auto">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-accent-500 rounded-full flex items-center justify-center">
                <FaGavel className="text-2xl text-white" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              خدماتنا القانونية المتخصصة
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-primary-100">
              نقدم مجموعة شاملة من الخدمات القانونية عالية الجودة لتلبية جميع احتياجاتكم القانونية
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="#services">
                <button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                  تصفح الخدمات
                </button>
              </Link>
              <Link href="/contact">
                <button className="border-2 border-white text-white hover:bg-white hover:text-primary-900 px-8 py-3 rounded-lg font-semibold transition-colors">
                  احجز استشارة
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-12 bg-gray-50" id="services">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search */}
                <div className="relative">
                  <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث في الخدمات..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-right"
                  />
                </div>

                {/* Category Filter */}
                <div className="relative">
                  <FaFilter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-right appearance-none"
                  >
                    <option value="">جميع التصنيفات</option>
                    {categories?.map(category => (
                      <option key={category._id} value={category._id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Tag Filter */}
                <div className="relative">
                  <FaTags className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث بالوسوم..."
                    value={selectedTag}
                    onChange={(e) => setSelectedTag(e.target.value)}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-right"
                  />
                </div>

                {/* Sort */}
                <div>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-right appearance-none"
                  >
                    <option value="latest">الأحدث</option>
                    <option value="popular">الأكثر مشاهدة</option>
                    <option value="rating">الأعلى تقييماً</option>
                    <option value="alphabetical">أبجدياً</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Services Grid */}
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredServices?.map(service => (
                  <ServiceCard key={service._id} service={service} />
                ))}
              </div>
            )}

            {(!filteredServices || filteredServices.length === 0) && !loading && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4 flex justify-center">
                  <FaSearch />
                </div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  لم يتم العثور على خدمات
                </h3>
                <p className="text-gray-500">
                  جرب تغيير معايير البحث أو الفلترة
                </p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              هل تحتاج إلى استشارة قانونية متخصصة؟
            </h2>
            <p className="text-xl mb-8 text-primary-100">
              فريقنا من المحامين المتخصصين جاهز لتقديم أفضل الخدمات القانونية لك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold transition-colors flex items-center justify-center">
                  <FaPhone className="ml-2" />
                  احجز استشارة مجانية
                </button>
              </Link>
              <Link href="mailto:<EMAIL>">
                <button className="border-2 border-white text-white hover:bg-white hover:text-primary-900 px-8 py-4 rounded-lg font-semibold transition-colors flex items-center justify-center">
                  <FaEnvelope className="ml-2" />
                  راسلنا الآن
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

// Service Card Component
const ServiceCard = ({ service }) => {
  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      {/* Service Image */}
      <div className="relative h-48 bg-gray-200">
        {service.coverImage || service.image ? (
          <Image
            src={service.coverImage || service.image}
            alt={service.title}
            fill
            className="object-cover"
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-gradient-to-r from-primary-100 to-primary-200">
            <FaGavel className="text-4xl text-primary-600" />
          </div>
        )}
        {service.featured && (
          <div className="absolute top-4 right-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
            مميز
          </div>
        )}
      </div>

      {/* Service Content */}
      <div className="p-6">
        {/* Category */}
        <div className="flex items-center mb-3">
          <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
            {service.category?.name || 'عام'}
          </span>
        </div>

        {/* Title */}
        <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
          {service.title}
        </h3>

        {/* Excerpt */}
        <p className="text-gray-600 mb-4 line-clamp-3">
          {service.excerpt || service.description}
        </p>

        {/* Meta Info */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
          <div className="flex items-center">
            <FaUser className="ml-1" />
            <span>{service.author?.username || 'المحرر'}</span>
          </div>
          <div className="flex items-center">
            <FaClock className="ml-1" />
            <span>{service.readTime || 5} دقائق</span>
          </div>
        </div>

        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center">
            <FaCalendar className="ml-1" />
            <span>{new Date(service.publishDate || service.created_at).toLocaleDateString('ar-SA')}</span>
          </div>
          <div className="flex items-center">
            <FaEye className="ml-1" />
            <span>{service.views || 0} مشاهدة</span>
          </div>
        </div>

        {/* Tags */}
        {service.tags && service.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {service.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}

        {/* Read More Button */}
        <Link href={`/services/${service.slug}`}>
          <button className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-lg font-semibold transition-colors flex items-center justify-center">
            اقرأ المزيد
            <FaArrowRight className="mr-2" />
          </button>
        </Link>
      </div>
    </div>
  );
};

// Server-side props
export async function getServerSideProps(context) {
  try {
    // جلب الخدمات والتصنيفات من API
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    const [servicesRes, categoriesRes] = await Promise.all([
      fetch(`${baseUrl}/api/services?limit=12`),
      fetch(`${baseUrl}/api/categories?type=service`)
    ]);

    const servicesData = await servicesRes.json();
    const categoriesData = await categoriesRes.json();

    return {
      props: {
        services: servicesData.services || [],
        categories: categoriesData.categories || [],
        totalServices: servicesData.total || 0,
        currentPage: 1,
        totalPages: Math.ceil((servicesData.total || 0) / 12)
      }
    };
  } catch (error) {
    console.error('Error fetching services:', error);
    return {
      props: {
        services: [],
        categories: [],
        totalServices: 0,
        currentPage: 1,
        totalPages: 0
      }
    };
  }
}

export default ServicesPage;

import { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import PerformanceStats from '../../components/admin/PerformanceStats';
import {
  FaNewspaper,
  FaConciergeBell,
  FaGavel,
  FaUsers,
  FaEnvelope,
  FaChartLine,
  FaEye,
  FaClock,
  FaArrowUp,
  FaArrowDown,
  FaCalendar
} from 'react-icons/fa';

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    articles: 0,
    services: 0,
    expertise: 0,
    messages: 0,
    recentArticles: [],
    recentMessages: [],
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);

      // جلب إحصائيات المقالات
      let articlesData = { success: false, articles: [] };
      try {
        const articlesResponse = await fetch('/api/admin/articles');
        if (articlesResponse.ok) {
          articlesData = await articlesResponse.json();
        }
      } catch (error) {
        console.error('Error fetching articles:', error);
      }

      // جلب إحصائيات الخدمات
      let servicesData = { success: false, services: [] };
      try {
        const servicesResponse = await fetch('/api/admin/services');
        if (servicesResponse.ok) {
          servicesData = await servicesResponse.json();
        }
      } catch (error) {
        console.error('Error fetching services:', error);
      }

      // جلب إحصائيات مجالات الخبرة
      let expertiseData = { success: false, expertiseAreas: [] };
      try {
        const expertiseResponse = await fetch('/api/admin/expertise');
        if (expertiseResponse.ok) {
          expertiseData = await expertiseResponse.json();
        }
      } catch (error) {
        console.error('Error fetching expertise:', error);
      }

      // جلب إحصائيات الرسائل (مؤقتاً معطل حتى يتم إنشاء API)
      let messagesData = { success: false, messages: [] };
      // try {
      //   const messagesResponse = await fetch('/api/admin/messages');
      //   if (messagesResponse.ok) {
      //     messagesData = await messagesResponse.json();
      //   }
      // } catch (error) {
      //   console.error('Error fetching messages:', error);
      // }

      setStats({
        articles: articlesData.success ? articlesData.articles.length : 0,
        services: servicesData.success ? servicesData.services.length : 0,
        expertise: expertiseData.success ? expertiseData.expertiseAreas.length : 0,
        messages: messagesData.success ? messagesData.messages.length : 0,
        recentArticles: articlesData.success ? articlesData.articles.slice(0, 5) : [],
        recentMessages: messagesData.success ? messagesData.messages.slice(0, 5) : [],
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const StatCard = ({ title, value, icon: Icon, color, change, changeType }) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-2">{loading ? '...' : value}</p>
          {change && (
            <div className={`flex items-center mt-2 text-sm ${
              changeType === 'increase' ? 'text-green-600' : 'text-red-600'
            }`}>
              {changeType === 'increase' ? <FaArrowUp className="ml-1" /> : <FaArrowDown className="ml-1" />}
              {change}% من الشهر الماضي
            </div>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
          <Icon className="text-2xl text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <AdminLayout title="لوحة المعلومات">
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold mb-2">مرحباً بك في لوحة التحكم! 👋</h1>
              <p className="text-primary-100">إليك نظرة عامة على أداء موقعك اليوم</p>
            </div>
            <div className="text-right">
              <div className="text-sm text-primary-200">اليوم</div>
              <div className="text-lg font-semibold">
                {new Date().toLocaleDateString('ar-SA', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="المقالات"
            value={stats.articles}
            icon={FaNewspaper}
            color="bg-blue-500"
            change={12}
            changeType="increase"
          />
          <StatCard
            title="الخدمات"
            value={stats.services}
            icon={FaConciergeBell}
            color="bg-green-500"
            change={8}
            changeType="increase"
          />
          <StatCard
            title="مجالات الخبرة"
            value={stats.expertise}
            icon={FaGavel}
            color="bg-purple-500"
            change={5}
            changeType="increase"
          />
          <StatCard
            title="رسائل التواصل"
            value={stats.messages}
            icon={FaEnvelope}
            color="bg-red-500"
            change={3}
            changeType="decrease"
          />
        </div>

        {/* Performance Stats */}
        <PerformanceStats />

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Articles */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900 flex items-center">
                <FaNewspaper className="text-blue-600 ml-3" />
                أحدث المقالات
              </h3>
              <a href="/admin/articles" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                عرض الكل
              </a>
            </div>

            {loading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : stats.recentArticles.length > 0 ? (
              <div className="space-y-4">
                {stats.recentArticles.map((article) => (
                  <div key={article.id} className="flex items-start space-x-3 space-x-reverse p-3 hover:bg-gray-50 rounded-lg transition-colors">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 line-clamp-1">{article.title}</h4>
                      <p className="text-sm text-gray-600 line-clamp-2 mt-1">{article.excerpt}</p>
                      <div className="flex items-center mt-2 text-xs text-gray-500">
                        <FaCalendar className="ml-1" />
                        {formatDate(article.created_at)}
                        <span className={`mr-3 px-2 py-1 rounded-full text-xs ${
                          article.status === 'published'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {article.status === 'published' ? 'منشور' : 'مسودة'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <FaNewspaper className="text-4xl mx-auto mb-2 opacity-50" />
                <p>لا توجد مقالات حديثة</p>
              </div>
            )}
          </div>

          {/* Recent Messages */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900 flex items-center">
                <FaEnvelope className="text-red-600 ml-3" />
                أحدث الرسائل
              </h3>
              <a href="/admin/messages" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                عرض الكل
              </a>
            </div>

            {loading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : stats.recentMessages.length > 0 ? (
              <div className="space-y-4">
                {stats.recentMessages.map((message) => (
                  <div key={message.id} className="flex items-start space-x-3 space-x-reverse p-3 hover:bg-gray-50 rounded-lg transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900">{message.name}</h4>
                        <span className="text-xs text-gray-500">{formatDate(message.created_at)}</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{message.email}</p>
                      <p className="text-sm text-gray-700 line-clamp-2 mt-2">{message.message}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <FaEnvelope className="text-4xl mx-auto mb-2 opacity-50" />
                <p>لا توجد رسائل حديثة</p>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-6">إجراءات سريعة</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a
              href="/admin/articles/new"
              className="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors group"
            >
              <FaNewspaper className="text-2xl text-blue-600 mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium text-blue-900">مقال جديد</span>
            </a>
            <a
              href="/admin/services/new"
              className="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors group"
            >
              <FaConciergeBell className="text-2xl text-green-600 mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium text-green-900">خدمة جديدة</span>
            </a>
            <a
              href="/admin/expertise/new"
              className="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors group"
            >
              <FaGavel className="text-2xl text-purple-600 mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium text-purple-900">مجال خبرة</span>
            </a>
            <a
              href="/admin/messages"
              className="flex flex-col items-center p-4 bg-red-50 hover:bg-red-100 rounded-lg transition-colors group"
            >
              <FaEnvelope className="text-2xl text-red-600 mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-sm font-medium text-red-900">الرسائل</span>
            </a>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '../../../../components/admin/AdminLayout';
import WYSIWYGEditor from '../../../../components/admin/WYSIWYGEditor';
import Link from 'next/link';
import {
  FaSave,
  FaArrowLeft,
  FaImage,
  FaUpload,
  FaTimes,
  FaCheck,
  FaExclamationTriangle,
  FaInfoCircle,
  FaUser,
  FaTags,
  FaCalendar,
  FaClock,
  FaEye,
  FaConciergeBell
} from 'react-icons/fa';

export default function EditService() {
  const router = useRouter();
  const { id } = router.query;
  const fileInputRef = useRef(null);

  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    description: '',
    content: '',
    image: '',
    coverImage: '',
    category: '',
    tags: [],
    status: 'draft',
    featured: false,
    author: '',
    publishDate: '',
    readTime: 5,
    relatedServices: [],
    requirements: '',
    duration: '',
    price: ''
  });

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [imagePreview, setImagePreview] = useState(null);
  const [coverImagePreview, setCoverImagePreview] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [imageUploading, setImageUploading] = useState(false);
  const [coverImageUploading, setCoverImageUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serviceCategories, setServiceCategories] = useState([]);
  const [authors, setAuthors] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [tagInput, setTagInput] = useState('');

  // Fetch service data and initial data
  useEffect(() => {
    if (id) {
      fetchService();
      fetchServiceCategories();
      fetchAuthors();
      fetchAvailableServices();
    }
  }, [id]);

  const fetchService = async () => {
    try {
      setInitialLoading(true);
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/services/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();

      if (data.success) {
        const service = data.service;
        setFormData({
          title: service.title || '',
          slug: service.slug || '',
          excerpt: service.excerpt || '',
          description: service.description || '',
          content: service.content || '',
          image: service.image || '',
          coverImage: service.coverImage || '',
          category: service.category?._id || service.category || '',
          tags: service.tags || [],
          status: service.status || 'draft',
          featured: service.featured || false,
          author: service.author?._id || service.author || '',
          publishDate: service.publishDate ? service.publishDate.split('T')[0] : '',
          readTime: service.readTime || 5,
          relatedServices: service.relatedServices?.map(s => s._id || s) || [],
          requirements: service.requirements || '',
          duration: service.duration || '',
          price: service.price || ''
        });

        if (service.image) setImagePreview(service.image);
        if (service.coverImage) setCoverImagePreview(service.coverImage);
      } else {
        setError(data.message || 'فشل في جلب بيانات الخدمة');
      }
    } catch (error) {
      console.error('Error fetching service:', error);
      setError('حدث خطأ في جلب بيانات الخدمة');
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchServiceCategories = async () => {
    try {
      const response = await fetch('/api/categories?type=service');
      const data = await response.json();
      if (data.success) {
        setServiceCategories(data.categories);
      }
    } catch (error) {
      console.error('Error fetching service categories:', error);
    }
  };

  const fetchAuthors = async () => {
    try {
      const response = await fetch('/api/admin/users?role=admin');
      const data = await response.json();
      if (data.success) {
        setAuthors(data.users);
      }
    } catch (error) {
      console.error('Error fetching authors:', error);
    }
  };

  const fetchAvailableServices = async () => {
    try {
      const response = await fetch('/api/services?limit=50');
      const data = await response.json();
      if (data.success) {
        setAvailableServices(data.services.filter(s => s._id !== id));
      }
    } catch (error) {
      console.error('Error fetching services:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    setError('');
    setSuccess('');

    // Auto-generate slug from title
    if (name === 'title') {
      const slug = value.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({
        ...prev,
        slug: slug
      }));
    }
  };

  const handleContentChange = (content) => {
    setFormData(prev => ({
      ...prev,
      content
    }));
  };

  // Handle tags
  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Handle related services
  const addRelatedService = (serviceId) => {
    if (!formData.relatedServices.includes(serviceId)) {
      setFormData(prev => ({
        ...prev,
        relatedServices: [...prev.relatedServices, serviceId]
      }));
    }
  };

  const removeRelatedService = (serviceId) => {
    setFormData(prev => ({
      ...prev,
      relatedServices: prev.relatedServices.filter(id => id !== serviceId)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (isSubmitting) return;

    setIsSubmitting(true);
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/services/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('تم تحديث الخدمة بنجاح');
        setTimeout(() => {
          router.push('/admin/services');
        }, 1500);
      } else {
        setError(result.message || 'حدث خطأ في تحديث الخدمة');
      }
    } catch (error) {
      console.error('Update error:', error);
      setError('حدث خطأ في تحديث الخدمة');
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  if (initialLoading) {
    return (
      <AdminLayout title="تحديث الخدمة">
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل بيانات الخدمة...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="تحديث الخدمة">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl shadow-lg text-white p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-2xl lg:text-3xl font-bold mb-2">✏️ تحديث الخدمة</h1>
              <p className="text-primary-100">تعديل بيانات الخدمة الموجودة</p>
            </div>

            <div className="flex items-center gap-3">
              <Link href="/admin/services">
                <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center text-sm">
                  <FaArrowLeft className="ml-2" />
                  العودة
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 flex items-start">
            <FaExclamationTriangle className="text-red-500 ml-3 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-800 font-medium">خطأ في التحديث</h4>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4 flex items-start">
            <FaCheck className="text-green-500 ml-3 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-green-800 font-medium">تم بنجاح</h4>
              <p className="text-green-700 text-sm">{success}</p>
            </div>
          </div>
        )}

        {/* Form - نفس النموذج من صفحة الإضافة مع تعديلات بسيطة */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-3">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* نفس الحقول من صفحة الإضافة */}
              {/* يمكن نسخها من new.js */}

              {/* Submit Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-end">
                <Link href="/admin/services">
                  <button
                    type="button"
                    className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center"
                  >
                    <FaTimes className="ml-2" />
                    إلغاء
                  </button>
                </Link>
                <button
                  type="submit"
                  disabled={loading || isSubmitting}
                  className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center disabled:opacity-50"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                      جاري التحديث...
                    </>
                  ) : (
                    <>
                      <FaSave className="ml-2" />
                      حفظ التغييرات
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Sidebar - نفس الشريط الجانبي من صفحة الإضافة */}
          <div className="lg:col-span-1">
            {/* يمكن نسخه من new.js */}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
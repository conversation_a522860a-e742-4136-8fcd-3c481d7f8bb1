// نظام تعدد اللغات للموقع
export const locales = ['ar', 'en'];
export const defaultLocale = 'ar';

// ترجمات الموقع
export const translations = {
  ar: {
    // Navigation
    nav: {
      home: 'الرئيسية',
      aboutUs: 'من نحن',
      services: 'الخدمات القانونية',
      legalResearch: 'التدريب والبحث القانوني',
      treasuresOfLaw: 'كنوز القانون',
      blog: 'المجلة القانونية',
      ourTeam: 'فريق العمل',
      careers: 'الوظائف',
      contactUs: 'اتصل بنا',
      freeConsultation: 'استشارة مجانية'
    },
    
    // Services
    services: {
      title: 'الخدمات القانونية',
      subtitle: 'خدمات قانونية شاملة ومتخصصة',
      judicialDisputes: 'المنازعات القضائية',
      corporateAndInvestment: 'الشركات والاستثمار',
      arbitrationAndDispute: 'التحكيم وتسوية المنازعات',
      legalAdvice: 'الاستشارات القانونية',
      propertySector: 'قطاع الملكية',
      foreignersAndInvestors: 'شئون الأجانب والمستثمرين',
      governmentRegulations: 'العلاقات الحكومية والتراخيص',
      contractsSector: 'قطاع العقود'
    },
    
    // Blog Systems
    blogSystems: {
      legalResearch: {
        title: 'التدريب والبحث القانوني',
        subtitle: 'برامج تدريبية وأبحاث قانونية متخصصة',
        legalTraining: 'التدريب القانوني والمهني',
        legalResearch: 'البحث القانوني'
      },
      treasuresOfLaw: {
        title: 'كنوز القانون',
        subtitle: 'مكتبة قانونية شاملة للمبادئ والتشريعات',
        legalPrinciples: 'كنوز المبادئ القانونية',
        legislations: 'كنوز التشريعات',
        legalBooks: 'كنوز الكتب القانونية',
        legalTemplates: 'كنوز الصيغ القانونية',
        contractTemplates: 'كنوز الصيغ التعاقدية'
      },
      blog: {
        title: 'المجلة القانونية',
        subtitle: 'أحدث الأخبار والمقالات القانونية',
        legislativeReleases: 'النشرات التشريعية والقانونية',
        updatedLegalPrinciples: 'المستحدث من المبادئ القانونية',
        commonLegalQuestions: 'أسئلة قانونية شائعة',
        judicialNews: 'الأخبار القضائية',
        latestResolutions: 'أحدث القرارات والتشريعات'
      }
    },
    
    // Common
    common: {
      readMore: 'اقرأ المزيد',
      viewAll: 'عرض الكل',
      search: 'البحث',
      categories: 'التصنيفات',
      tags: 'الكلمات المفتاحية',
      date: 'التاريخ',
      author: 'الكاتب',
      share: 'مشاركة',
      print: 'طباعة',
      download: 'تحميل',
      back: 'العودة',
      next: 'التالي',
      previous: 'السابق',
      loading: 'جاري التحميل...',
      noResults: 'لا توجد نتائج',
      error: 'حدث خطأ'
    },
    
    // Footer
    footer: {
      aboutUs: 'من نحن',
      quickLinks: 'روابط سريعة',
      services: 'خدماتنا',
      contact: 'معلومات التواصل',
      followUs: 'تابعنا',
      allRightsReserved: 'جميع الحقوق محفوظة',
      privacyPolicy: 'سياسة الخصوصية',
      termsOfUse: 'شروط الاستخدام'
    }
  },
  
  en: {
    // Navigation
    nav: {
      home: 'Home',
      aboutUs: 'About Us',
      services: 'Legal Services',
      legalResearch: 'Legal Research & Training',
      treasuresOfLaw: 'Treasures of Law',
      blog: 'Legal Magazine',
      ourTeam: 'Our Team',
      careers: 'Careers',
      contactUs: 'Contact Us',
      freeConsultation: 'Free Consultation'
    },
    
    // Services
    services: {
      title: 'Legal Services',
      subtitle: 'Comprehensive and specialized legal services',
      judicialDisputes: 'Judicial Disputes',
      corporateAndInvestment: 'Corporate & Investment',
      arbitrationAndDispute: 'Arbitration & Dispute Settlement',
      legalAdvice: 'Legal Advice',
      propertySector: 'Property Sector',
      foreignersAndInvestors: 'Foreigners & Investors Affairs',
      governmentRegulations: 'Government Relations & Licensing',
      contractsSector: 'Contracts Sector'
    },
    
    // Blog Systems
    blogSystems: {
      legalResearch: {
        title: 'Legal Research & Training',
        subtitle: 'Specialized training programs and legal research',
        legalTraining: 'Legal & Vocational Training',
        legalResearch: 'Legal Research'
      },
      treasuresOfLaw: {
        title: 'Treasures of Law',
        subtitle: 'Comprehensive legal library of principles and legislation',
        legalPrinciples: 'Legal Principles Treasures',
        legislations: 'Legislation Treasures',
        legalBooks: 'Legal Books Treasures',
        legalTemplates: 'Legal Templates Treasures',
        contractTemplates: 'Contract Templates Treasures'
      },
      blog: {
        title: 'Legal Magazine',
        subtitle: 'Latest legal news and articles',
        legislativeReleases: 'Legislative & Legal Releases',
        updatedLegalPrinciples: 'Updated Legal Principles',
        commonLegalQuestions: 'Common Legal Questions',
        judicialNews: 'Judicial News',
        latestResolutions: 'Latest Resolutions & Legislation'
      }
    },
    
    // Common
    common: {
      readMore: 'Read More',
      viewAll: 'View All',
      search: 'Search',
      categories: 'Categories',
      tags: 'Tags',
      date: 'Date',
      author: 'Author',
      share: 'Share',
      print: 'Print',
      download: 'Download',
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      loading: 'Loading...',
      noResults: 'No Results',
      error: 'An Error Occurred'
    },
    
    // Footer
    footer: {
      aboutUs: 'About Us',
      quickLinks: 'Quick Links',
      services: 'Our Services',
      contact: 'Contact Information',
      followUs: 'Follow Us',
      allRightsReserved: 'All Rights Reserved',
      privacyPolicy: 'Privacy Policy',
      termsOfUse: 'Terms of Use'
    }
  }
};

// دالة للحصول على الترجمة
export function getTranslation(locale, key) {
  const keys = key.split('.');
  let translation = translations[locale] || translations[defaultLocale];
  
  for (const k of keys) {
    translation = translation[k];
    if (!translation) break;
  }
  
  return translation || key;
}

// دالة للحصول على اتجاه النص
export function getDirection(locale) {
  return locale === 'ar' ? 'rtl' : 'ltr';
}

// دالة للحصول على اللغة المقابلة
export function getAlternateLocale(locale) {
  return locale === 'ar' ? 'en' : 'ar';
}

// دالة لتحويل المسار حسب اللغة
export function getLocalizedPath(path, locale) {
  // إزالة اللغة الحالية من المسار إن وجدت
  const cleanPath = path.replace(/^\/(ar|en)/, '');
  
  // إضافة اللغة الجديدة
  return `/${locale}${cleanPath}`;
}

// دالة للتحقق من صحة اللغة
export function isValidLocale(locale) {
  //return locales.includes(locale);
  return true;
}

// دالة لاستخراج اللغة من المسار
export function getLocaleFromPath(path) {
  const segments = path.split('/');
  const potentialLocale = segments[1];
  
  if (isValidLocale(potentialLocale)) {
    return potentialLocale;
  }
  
  return defaultLocale;
}

import { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import {
  FaNewspaper,
  FaImages,
  FaUsers,
  FaEnvelope,
  FaEye,
  FaEdit,
  FaPlus,
  FaChartLine,
  FaRocket,
  FaLaptop
} from 'react-icons/fa';

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    articles: 0,
    slides: 0,
    teamMembers: 0,
    messages: 0
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // هنا يمكن إضافة API calls للحصول على الإحصائيات
      // مؤقتاً سنستخدم بيانات وهمية
      setStats({
        articles: 12,
        slides: 5,
        teamMembers: 8,
        messages: 23
      });

      setRecentActivity([
        { id: 1, type: 'article', action: 'تم إنشاء مقال جديد', title: 'قانون الشركات الجديد', time: 'منذ ساعتين' },
        { id: 2, type: 'message', action: 'رسالة جديدة', title: 'استفسار عن الخدمات', time: 'منذ 3 ساعات' },
        { id: 3, type: 'slide', action: 'تم تحديث شريحة', title: 'الشريحة الرئيسية', time: 'منذ يوم' }
      ]);

      setLoading(false);
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error);
      setLoading(false);
    }
  };

  const statCards = [
    {
      title: 'المقالات',
      value: stats.articles,
      icon: FaNewspaper,
      color: 'bg-blue-500',
      href: '/admin/articles'
    },
    {
      title: 'الشرائح',
      value: stats.slides,
      icon: FaImages,
      color: 'bg-green-500',
      href: '/admin/slides'
    },
    {
      title: 'أعضاء الفريق',
      value: stats.teamMembers,
      icon: FaUsers,
      color: 'bg-purple-500',
      href: '/admin/team'
    },
    {
      title: 'الرسائل',
      value: stats.messages,
      icon: FaEnvelope,
      color: 'bg-red-500',
      href: '/admin/messages'
    }
  ];

  const quickActions = [
    {
      title: 'إضافة مقال جديد',
      description: 'إنشاء مقال أو خبر جديد',
      icon: FaPlus,
      href: '/admin/articles/new',
      color: 'bg-blue-600'
    },
    {
      title: 'إضافة خدمة إلكترونية',
      description: 'إنشاء خدمة إلكترونية جديدة',
      icon: FaLaptop,
      href: '/admin/electronic-services/new',
      color: 'bg-indigo-600'
    },
    {
      title: 'إدارة الشرائح',
      description: 'تحديث شرائح الصفحة الرئيسية',
      icon: FaImages,
      href: '/admin/slides',
      color: 'bg-green-600'
    },
    {
      title: 'إضافة عضو فريق',
      description: 'إضافة عضو جديد إلى الفريق',
      icon: FaUsers,
      href: '/admin/team/new',
      color: 'bg-purple-600'
    },
    {
      title: 'الرسائل الجديدة',
      description: 'مراجعة رسائل العملاء',
      icon: FaEnvelope,
      href: '/admin/messages',
      color: 'bg-red-600'
    },
    {
      title: 'عرض الموقع',
      description: 'مراجعة الموقع كما يراه الزوار',
      icon: FaEye,
      href: '/',
      color: 'bg-gray-600',
      external: true
    }
  ];

  if (loading) {
    return (
      <AdminLayout title="لوحة التحكم">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-900 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="لوحة التحكم">
      <div className="space-y-6">
        {/* Welcome Message */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl shadow-lg p-6 lg:p-8 text-white mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-2xl lg:text-3xl font-bold mb-2">
                مرحباً بك في لوحة التحكم 👋
              </h1>
              <p className="text-primary-100 text-base lg:text-lg">
                إدارة شاملة ومتطورة لموقع مكتب المحاماة
              </p>
            </div>
            <div className="lg:flex-shrink-0">
              <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center">
                <div className="text-sm opacity-90">تاريخ اليوم</div>
                <div className="text-lg font-semibold">
                  {new Date().toLocaleDateString('ar-SA', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {statCards.map((card, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-4 lg:p-6 border border-gray-100 hover:border-primary-200">
              <div className="flex items-center justify-between mb-4">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-600 mb-1 truncate">{card.title}</p>
                  <p className="text-2xl lg:text-3xl font-bold text-gray-900">{card.value}</p>
                </div>
                <div className={`${card.color} rounded-xl p-3 lg:p-4 shadow-lg flex-shrink-0`}>
                  <card.icon className="h-6 w-6 lg:h-8 lg:w-8 text-white" />
                </div>
              </div>
              <div>
                <a
                  href={card.href}
                  className="inline-flex items-center text-sm text-primary-600 hover:text-primary-800 font-medium group"
                >
                  عرض التفاصيل
                  <svg className="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </a>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center">
                <div className="bg-primary-100 rounded-lg p-2 ml-3">
                  <FaRocket className="h-5 w-5 text-primary-600" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">إجراءات سريعة</h2>
              </div>
            </div>
            <div className="p-6 space-y-3">
              {quickActions.map((action, index) => (
                <a
                  key={index}
                  href={action.href}
                  target={action.external ? '_blank' : '_self'}
                  className="flex items-center p-4 border border-gray-100 rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-primary-50 hover:border-primary-200 transition-all duration-300 group"
                >
                  <div className={`${action.color} rounded-xl p-3 shadow-md group-hover:shadow-lg transition-shadow`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="mr-4 flex-1">
                    <h3 className="font-semibold text-gray-900 group-hover:text-primary-700 transition-colors">{action.title}</h3>
                    <p className="text-sm text-gray-600">{action.description}</p>
                  </div>
                  <div className="text-gray-400 group-hover:text-primary-500 transition-colors">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </div>
                </a>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-2 ml-3">
                  <FaChartLine className="h-5 w-5 text-green-600" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">النشاط الأخير</h2>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentActivity.length > 0 ? recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-md">
                        <FaNewspaper className="w-5 h-5 text-white" />
                      </div>
                    </div>
                    <div className="mr-4 flex-1">
                      <p className="text-sm font-semibold text-gray-900 mb-1">
                        {activity.action}
                      </p>
                      <p className="text-sm text-gray-600 mb-1">{activity.title}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                )) : (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FaChartLine className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-gray-500">لا يوجد نشاط حديث</p>
                    <p className="text-sm text-gray-400">ابدأ بإضافة محتوى جديد لرؤية النشاط هنا</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}

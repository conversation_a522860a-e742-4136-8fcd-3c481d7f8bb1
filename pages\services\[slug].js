import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../../components/Layout';
import {
  FaArrowLeft,
  FaClock,
  FaEye,
  FaUser,
  FaCalendar,
  FaTags,
  FaShare,
  FaFacebook,
  FaTwitter,
  FaLinkedin,
  FaWhatsapp,
  FaGavel,
  FaBookmark,
  FaPrint,
  FaEnvelope,
  FaPhone,
  FaQuoteLeft,
  FaChevronRight
} from 'react-icons/fa';

export default function ServicePage() {
  const router = useRouter();
  const { slug } = router.query;
  const [service, setService] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [relatedServices, setRelatedServices] = useState([]);

  useEffect(() => {
    if (slug) {
      fetchService();
    }
  }, [slug]);

  const fetchService = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/services/${slug}`);
      const data = await response.json();

      if (data.success) {
        setService(data.service);
        // جلب الخدمات ذات الصلة
        fetchRelatedServices(data.service.category);
      } else {
        setError(data.message || 'الخدمة غير موجودة');
      }
    } catch (error) {
      console.error('Error fetching service:', error);
      setError('حدث خطأ في جلب الخدمة');
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedServices = async (category) => {
    try {
      const response = await fetch(`/api/services?category=${category}&limit=3`);
      const data = await response.json();

      if (data.success) {
        // استبعاد الخدمة الحالية من الخدمات ذات الصلة
        const filtered = data.services.filter(s => s.slug !== slug);
        setRelatedServices(filtered.slice(0, 3));
      }
    } catch (error) {
      console.error('Error fetching related services:', error);
    }
  };

  const shareService = (platform) => {
    const url = window.location.href;
    const title = service?.title || '';
    const description = service?.description || '';

    const shareUrls = {
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
      whatsapp: `https://wa.me/?text=${encodeURIComponent(title + ' - ' + url)}`
    };

    if (shareUrls[platform]) {
      window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الخدمة...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !service) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="text-6xl text-gray-400 mb-4">🔍</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">الخدمة غير موجودة</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <Link href="/services">
              <button className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors">
                العودة إلى الخدمات
              </button>
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>{service.title} - الخدمات الإلكترونية</title>
        <meta name="description" content={service.description} />
        <meta property="og:title" content={service.title} />
        <meta property="og:description" content={service.description} />
        <meta property="og:image" content={service.image} />
        <meta property="og:type" content="article" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={service.title} />
        <meta name="twitter:description" content={service.description} />
        <meta name="twitter:image" content={service.image} />
      </Head>

      <Layout>
        <div className="min-h-screen bg-gray-50">
          {/* Hero Section with Cover Image */}
          <div className="relative bg-gradient-to-r from-primary-900 to-primary-700">
            {service.coverImage && (
              <div className="absolute inset-0">
                <img
                  src={service.coverImage}
                  alt={service.title}
                  className="w-full h-full object-cover opacity-30"
                />
              </div>
            )}
            <div className="relative bg-black bg-opacity-50">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                {/* Breadcrumb */}
                <nav className="flex items-center space-x-2 space-x-reverse text-sm mb-8">
                  <Link href="/" className="text-white hover:text-accent-400">
                    الرئيسية
                  </Link>
                  <FaChevronRight className="text-white text-xs" />
                  <Link href="/services" className="text-white hover:text-accent-400">
                    الخدمات القانونية
                  </Link>
                  <FaChevronRight className="text-white text-xs" />
                  <span className="text-accent-400">{service.title}</span>
                </nav>

                {/* Service Header */}
                <div className="max-w-4xl">
                  {/* Category */}
                  <div className="flex items-center mb-4">
                    <span className="bg-accent-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                      {service.category?.name || service.category}
                    </span>
                  </div>

                  {/* Title */}
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                    {service.title}
                  </h1>

                  {/* Excerpt */}
                  <p className="text-xl text-gray-200 mb-8 leading-relaxed">
                    {service.excerpt || service.description}
                  </p>

                  {/* Meta Information */}
                  <div className="flex flex-wrap items-center gap-6 text-white">
                    <div className="flex items-center">
                      <FaUser className="ml-2" />
                      <span>{service.author?.username || 'المحرر'}</span>
                    </div>
                    <div className="flex items-center">
                      <FaCalendar className="ml-2" />
                      <span>{new Date(service.publishDate || service.created_at).toLocaleDateString('ar-SA')}</span>
                    </div>
                    <div className="flex items-center">
                      <FaClock className="ml-2" />
                      <span>{service.readTime || 5} دقائق قراءة</span>
                    </div>
                    <div className="flex items-center">
                      <FaEye className="ml-2" />
                      <span>{service.views || 0} مشاهدة</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-3">
                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3 mb-8">
                  <button className="flex items-center bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <FaBookmark className="ml-2" />
                    حفظ
                  </button>
                  <button className="flex items-center bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <FaPrint className="ml-2" />
                    طباعة
                  </button>
                  <button className="flex items-center bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <FaShare className="ml-2" />
                    مشاركة
                  </button>
                </div>

                {/* Service Content */}
                <article className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
                  <div
                    className="prose prose-lg prose-primary max-w-none"
                    style={{
                      fontSize: '18px',
                      lineHeight: '1.8',
                      color: '#374151'
                    }}
                    dangerouslySetInnerHTML={{ __html: service.content }}
                  />
                </article>

                {/* Tags Section */}
                {service.tags && service.tags.length > 0 && (
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
                    <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                      <FaTags className="text-primary-600 ml-3" />
                      الوسوم
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {service.tags.map((tag, index) => (
                        <Link key={index} href={`/services?tag=${tag}`}>
                          <span className="bg-primary-100 text-primary-800 px-3 py-2 rounded-full text-sm font-medium hover:bg-primary-200 transition-colors cursor-pointer">
                            #{tag}
                          </span>
                        </Link>
                      ))}
                    </div>
                  </div>
                )}

                {/* Author Info */}
                {service.author && (
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">عن المستشار القانوني</h3>
                    <div className="flex items-start">
                      <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center ml-4">
                        <FaUser className="text-primary-600 text-xl" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 mb-2">
                          {service.author.username || service.author}
                        </h4>
                        <p className="text-gray-600 text-sm mb-3">
                          مستشار قانوني متخصص في {service.category?.name || service.category}
                        </p>
                        <div className="flex gap-3">
                          <button className="flex items-center bg-primary-600 text-white px-3 py-1 rounded text-sm hover:bg-primary-700 transition-colors">
                            <FaEnvelope className="ml-1" />
                            راسل المستشار
                          </button>
                          <button className="flex items-center bg-accent-500 text-white px-3 py-1 rounded text-sm hover:bg-accent-600 transition-colors">
                            <FaPhone className="ml-1" />
                            احجز استشارة
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Share Section */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <FaShare className="text-primary-600 ml-3" />
                    شارك هذه الخدمة
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    <button
                      onClick={() => shareService('facebook')}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                    >
                      <FaFacebook className="ml-2" />
                      فيسبوك
                    </button>
                    <button
                      onClick={() => shareService('twitter')}
                      className="bg-sky-500 text-white px-4 py-2 rounded-lg hover:bg-sky-600 transition-colors flex items-center"
                    >
                      <FaTwitter className="ml-2" />
                      تويتر
                    </button>
                    <button
                      onClick={() => shareService('linkedin')}
                      className="bg-blue-700 text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors flex items-center"
                    >
                      <FaLinkedin className="ml-2" />
                      لينكد إن
                    </button>
                    <button
                      onClick={() => shareService('whatsapp')}
                      className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
                    >
                      <FaWhatsapp className="ml-2" />
                      واتساب
                    </button>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                {/* Quick Contact */}
                <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white mb-6">
                  <h3 className="text-lg font-bold mb-4">تحتاج مساعدة؟</h3>
                  <p className="text-primary-100 mb-4 text-sm">
                    تواصل معنا للحصول على استشارة قانونية متخصصة
                  </p>
                  <div className="space-y-3">
                    <button className="w-full bg-white text-primary-600 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center">
                      <FaPhone className="ml-2" />
                      اتصل بنا
                    </button>
                    <button className="w-full bg-accent-500 text-white py-2 rounded-lg font-semibold hover:bg-accent-600 transition-colors flex items-center justify-center">
                      <FaEnvelope className="ml-2" />
                      راسلنا
                    </button>
                  </div>
                </div>

                {/* Service Stats */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">إحصائيات الخدمة</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">المشاهدات</span>
                      <span className="font-semibold text-gray-900">{service.views || 0}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">وقت القراءة</span>
                      <span className="font-semibold text-gray-900">{service.readTime || 5} دقائق</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">تاريخ النشر</span>
                      <span className="font-semibold text-gray-900">
                        {new Date(service.publishDate || service.created_at).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                    {service.rating && service.rating.average > 0 && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">التقييم</span>
                        <span className="font-semibold text-gray-900">
                          {service.rating.average.toFixed(1)} ⭐
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Related Services */}
                {relatedServices.length > 0 && (
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">خدمات ذات صلة</h3>
                    <div className="space-y-3">
                      {relatedServices.map((relatedService) => (
                        <Link key={relatedService._id} href={`/services/${relatedService.slug}`}>
                          <div className="border border-gray-200 rounded-lg p-3 hover:border-primary-300 hover:shadow-md transition-all cursor-pointer">
                            <div className="flex items-start">
                              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center ml-3 flex-shrink-0">
                                <FaGavel className="text-primary-600" />
                              </div>
                              <div className="flex-1">
                                <h4 className="font-semibold text-gray-900 mb-1 text-sm line-clamp-2">
                                  {relatedService.title}
                                </h4>
                                <p className="text-gray-600 text-xs line-clamp-2">
                                  {relatedService.excerpt || relatedService.description}
                                </p>
                                <div className="flex items-center mt-2 text-xs text-gray-500">
                                  <FaEye className="ml-1" />
                                  <span>{relatedService.views || 0}</span>
                                  <span className="mx-2">•</span>
                                  <FaClock className="ml-1" />
                                  <span>{relatedService.readTime || 5} دقائق</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>

                    <Link href="/services">
                      <button className="w-full mt-4 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm">
                        عرض جميع الخدمات
                      </button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
}
import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '../../../components/admin/AdminLayout';
import WYSIWYGEditor from '../../../components/admin/WYSIWYGEditor';
import Link from 'next/link';
import {
  FaSave,
  FaArrowLeft,
  FaImage,
  FaUpload,
  FaStar,
  FaEye,
  FaTimes,
  FaCheck,
  FaExclamationTriangle,
  FaInfoCircle,
  FaUser,
  FaTag,
  FaCalendar,
  FaGlobe,
  FaClock,
  FaFileAlt,
  FaCog,
  FaFont,
  FaFilePdf,
  FaConciergeBell,
  FaDollarSign,
  FaClipboardList
} from 'react-icons/fa';

export default function NewService() {
  const router = useRouter();
  const fileInputRef = useRef(null);

  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    description: '',
    content: '',
    image: '',
    coverImage: '',
    category: '',
    tags: [],
    status: 'draft',
    featured: false,
    author: '',
    publishDate: new Date().toISOString().split('T')[0],
    readTime: 5,
    relatedServices: [],
    requirements: '',
    duration: '',
    price: '',
    pdf_file: null
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [imagePreview, setImagePreview] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [savedDraft, setSavedDraft] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [pdfUploading, setPdfUploading] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);
  const [coverImageUploading, setCoverImageUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serviceCategories, setServiceCategories] = useState([]);
  const [authors, setAuthors] = useState([]);
  const [availableServices, setAvailableServices] = useState([]);
  const [tagInput, setTagInput] = useState('');
  const [coverImagePreview, setCoverImagePreview] = useState(null);

  // Fetch initial data
  useEffect(() => {
    fetchServiceCategories();
    fetchAuthors();
    fetchAvailableServices();
  }, []);

  const fetchServiceCategories = async () => {
    try {
      const response = await fetch('/api/categories?type=service');
      const data = await response.json();
      if (data.success) {
        setServiceCategories(data.categories);
      }
    } catch (error) {
      console.error('Error fetching service categories:', error);
    }
  };

  const fetchAuthors = async () => {
    try {
      const response = await fetch('/api/admin/users?role=admin');
      const data = await response.json();
      if (data.success) {
        setAuthors(data.users);
      }
    } catch (error) {
      console.error('Error fetching authors:', error);
    }
  };

  const fetchAvailableServices = async () => {
    try {
      const response = await fetch('/api/services?limit=50');
      const data = await response.json();
      if (data.success) {
        setAvailableServices(data.services);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
    }
  };

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveEnabled && (formData.title || formData.content)) {
      const timer = setTimeout(() => {
        saveDraft();
      }, 30000); // Auto-save every 30 seconds

      return () => {
        clearTimeout(timer);
      };
    }
  }, [formData, autoSaveEnabled]);

  // Load draft on component mount
  useEffect(() => {
    loadDraft();
  }, []);

  const validateField = (name, value) => {
    const errors = { ...validationErrors };

    switch (name) {
      case 'title':
        if (!value.trim()) {
          errors.title = 'العنوان مطلوب';
        } else if (value.length < 5) {
          errors.title = 'العنوان يجب أن يكون 5 أحرف على الأقل';
        } else if (value.length > 100) {
          errors.title = 'العنوان يجب أن يكون أقل من 100 حرف';
        } else {
          delete errors.title;
        }
        break;
      case 'content':
        const textContent = value.replace(/<[^>]*>/g, '');
        if (!textContent.trim()) {
          errors.content = 'المحتوى مطلوب';
        } else if (textContent.length < 50) {
          errors.content = 'المحتوى يجب أن يكون 50 حرف على الأقل';
        } else {
          delete errors.content;
        }
        break;
      case 'description':
        if (!value.trim()) {
          errors.description = 'الوصف مطلوب';
        } else if (value.length > 300) {
          errors.description = 'الوصف يجب أن يكون أقل من 300 حرف';
        } else {
          delete errors.description;
        }
        break;
      default:
        break;
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    setError('');
    setSuccess('');

    // Validate field
    validateField(name, newValue);

    // Auto-generate slug from title
    if (name === 'title') {
      const slug = value.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({
        ...prev,
        slug: slug
      }));
    }
  };

  // Handle tags
  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Handle related services
  const addRelatedService = (serviceId) => {
    if (!formData.relatedServices.includes(serviceId)) {
      setFormData(prev => ({
        ...prev,
        relatedServices: [...prev.relatedServices, serviceId]
      }));
    }
  };

  const removeRelatedService = (serviceId) => {
    setFormData(prev => ({
      ...prev,
      relatedServices: prev.relatedServices.filter(id => id !== serviceId)
    }));
  };

  const handleContentChange = (content) => {
    setFormData(prev => ({
      ...prev,
      content
    }));
    validateField('content', content);
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
      setError('يُسمح فقط بملفات الصور');
      return;
    }

    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
      return;
    }

    setImageUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setFormData(prev => ({
          ...prev,
          image: result.image.url
        }));
        setImagePreview(result.image.url);
        setSuccess('تم رفع الصورة بنجاح');
      } else {
        setError(result.error || 'خطأ في رفع الصورة');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError('خطأ في رفع الصورة');
    } finally {
      setImageUploading(false);
    }
  };

  const removeImage = () => {
    setFormData(prev => ({
      ...prev,
      image: ''
    }));
    setImagePreview('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleCoverImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
      setError('يُسمح فقط بملفات الصور');
      return;
    }

    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
      return;
    }

    setCoverImageUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setFormData(prev => ({
          ...prev,
          coverImage: result.image.url
        }));
        setCoverImagePreview(result.image.url);
        setSuccess('تم رفع صورة الغلاف بنجاح');
      } else {
        setError(result.error || 'خطأ في رفع الصورة');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError('خطأ في رفع الصورة');
    } finally {
      setCoverImageUploading(false);
    }
  };

  const removeCoverImage = () => {
    setFormData(prev => ({
      ...prev,
      coverImage: ''
    }));
    setCoverImagePreview('');
  };

  const handlePDFUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (file.type !== 'application/pdf') {
      setError('يُسمح فقط بملفات PDF');
      return;
    }

    // التحقق من حجم الملف (20MB)
    if (file.size > 20 * 1024 * 1024) {
      setError('حجم الملف يجب أن يكون أقل من 20 ميجابايت');
      return;
    }

    setPdfUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('pdf', file);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000);

      const response = await fetch('/api/upload/pdf', {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const result = await response.json();

      if (result.success) {
        setFormData(prev => ({
          ...prev,
          pdf_file: result.file
        }));
        setSuccess('تم رفع ملف PDF بنجاح');
      } else {
        setError(result.error || 'خطأ في رفع الملف');
      }
    } catch (error) {
      console.error('Upload error:', error);
      if (error.name === 'AbortError') {
        setError('انتهت مهلة رفع الملف. يرجى المحاولة مرة أخرى.');
      } else if (error.message.includes('Failed to fetch')) {
        setError('خطأ في الاتصال. تحقق من الإنترنت وحاول مرة أخرى.');
      } else {
        setError('خطأ في رفع الملف: ' + (error.message || 'خطأ غير معروف'));
      }
    } finally {
      setPdfUploading(false);
    }
  };

  const removePDF = () => {
    setFormData(prev => ({
      ...prev,
      pdf_file: null
    }));
  };

  const saveDraft = async () => {
    try {
      localStorage.setItem('serviceDraft', JSON.stringify(formData));
      setSavedDraft(true);
      setTimeout(() => setSavedDraft(false), 2000);
    } catch (error) {
      console.error('خطأ في حفظ المسودة:', error);
    }
  };

  const loadDraft = () => {
    try {
      const draft = localStorage.getItem('serviceDraft');
      if (draft) {
        const draftData = JSON.parse(draft);
        setFormData(draftData);
        if (draftData.image) {
          setImagePreview(draftData.image);
        }
      }
    } catch (error) {
      console.error('خطأ في تحميل المسودة:', error);
    }
  };

  const clearDraft = () => {
    localStorage.removeItem('serviceDraft');
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.title.trim()) errors.title = 'العنوان مطلوب';
    if (!formData.content.trim()) errors.content = 'المحتوى مطلوب';
    if (!formData.description.trim()) errors.description = 'الوصف مطلوب';
    if (!formData.category.trim()) errors.category = 'التصنيف مطلوب';
    if (formData.title.length < 5) errors.title = 'العنوان قصير جداً';

    const textContent = formData.content.replace(/<[^>]*>/g, '');
    if (textContent.length < 50) errors.content = 'المحتوى قصير جداً';

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e, shouldRedirect = true) => {
    if (e && e.preventDefault) {
      e.preventDefault();
    }

    // منع الإرسال المتعدد
    if (isSubmitting) {
      return;
    }

    if (!validateForm()) {
      setError('يرجى تصحيح الأخطاء قبل الحفظ');
      return;
    }

    setIsSubmitting(true);
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('تم إنشاء الخدمة بنجاح');
        clearDraft();

        if (shouldRedirect) {
          setTimeout(() => {
            router.push('/admin/services');
          }, 1500);
        }
      } else {
        setError(result.message || 'حدث خطأ في إنشاء الخدمة');
      }
    } catch (error) {
      console.error('Submit error:', error);
      setError('حدث خطأ في إنشاء الخدمة');
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  const getProgressPercentage = () => {
    let completed = 0;
    const total = 6;

    if (formData.title) completed++;
    if (formData.content) completed++;
    if (formData.description) completed++;
    if (formData.category) completed++;
    if (formData.image) completed++;
    if (formData.requirements) completed++;

    return Math.round((completed / total) * 100);
  };

  return (
    <AdminLayout title="إضافة خدمة جديدة">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl shadow-lg text-white p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-2xl lg:text-3xl font-bold mb-2">🛎️ إضافة خدمة جديدة</h1>
              <p className="text-primary-100">إنشاء خدمة جديدة للعملاء</p>
            </div>

            <div className="flex items-center gap-3">
              {/* Progress Indicator */}
              <div className="hidden lg:flex items-center gap-3 bg-white/10 rounded-lg px-4 py-2">
                <FaConciergeBell className="text-primary-200" />
                <div className="text-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <span>التقدم:</span>
                    <span className="font-bold">{getProgressPercentage()}%</span>
                  </div>
                  <div className="w-24 bg-white/20 rounded-full h-1">
                    <div
                      className="bg-white h-1 rounded-full transition-all duration-300"
                      style={{ width: `${getProgressPercentage()}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* Auto-save indicator */}
              <div className="flex items-center gap-2 bg-white/10 rounded-lg px-3 py-2">
                <FaClock className="text-primary-200" />
                <span className="text-sm">
                  {savedDraft ? 'تم الحفظ!' : 'حفظ تلقائي'}
                </span>
              </div>

              {/* Actions */}
              <button
                onClick={saveDraft}
                className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center text-sm"
              >
                <FaSave className="ml-2" />
                حفظ مسودة
              </button>

              <Link href="/admin/services">
                <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center text-sm">
                  <FaArrowLeft className="ml-2" />
                  العودة
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 flex items-start">
            <FaExclamationTriangle className="text-red-500 ml-3 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-800 font-medium">خطأ في الحفظ</h4>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4 flex items-start">
            <FaCheck className="text-green-500 ml-3 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-green-800 font-medium">تم بنجاح</h4>
              <p className="text-green-700 text-sm">{success}</p>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Form Content */}
          <div className="lg:col-span-3">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center ml-3">
                    <span className="text-primary-600 font-bold text-lg">1</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">المعلومات الأساسية</h3>
                    <p className="text-sm text-gray-600">العنوان والوصف والرابط المختصر</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaConciergeBell className="inline ml-1" />
                      عنوان الخدمة *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        validationErrors.title ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="مثال: استخراج شهادة ميلاد"
                    />
                    {validationErrors.title && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.title}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaGlobe className="inline ml-1" />
                      الرابط المختصر
                    </label>
                    <input
                      type="text"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="birth-certificate"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaTag className="inline ml-1" />
                      التصنيف *
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        validationErrors.category ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">اختر التصنيف</option>
                      {serviceCategories.map((category) => (
                        <option key={category.id} value={category.name}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                    {validationErrors.category && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.category}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaInfoCircle className="inline ml-1" />
                      وصف مختصر للخدمة *
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        validationErrors.description ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="وصف مختصر يوضح ما تقدمه هذه الخدمة..."
                    />
                    {validationErrors.description && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.description}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Content Editor */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center ml-3">
                    <span className="text-primary-600 font-bold text-lg">2</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">محتوى الخدمة</h3>
                    <p className="text-sm text-gray-600">الشرح التفصيلي للخدمة</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700">
                    <FaFileAlt className="inline ml-1" />
                    المحتوى التفصيلي *
                  </label>
                  <WYSIWYGEditor
                    content={formData.content}
                    onChange={handleContentChange}
                    placeholder="اكتب الشرح التفصيلي للخدمة هنا..."
                    height={400}
                  />
                  {validationErrors.content && (
                    <p className="text-red-500 text-sm">{validationErrors.content}</p>
                  )}
                </div>
              </div>

              {/* Service Details */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center ml-3">
                    <span className="text-primary-600 font-bold text-lg">3</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">تفاصيل الخدمة</h3>
                    <p className="text-sm text-gray-600">المتطلبات والمدة والتكلفة</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaClipboardList className="inline ml-1" />
                      المتطلبات
                    </label>
                    <textarea
                      name="requirements"
                      value={formData.requirements}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="مثال: الهوية الوطنية، شهادة الراتب..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaClock className="inline ml-1" />
                      مدة الإنجاز
                    </label>
                    <input
                      type="text"
                      name="duration"
                      value={formData.duration}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="مثال: 3-5 أيام عمل"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaDollarSign className="inline ml-1" />
                      التكلفة
                    </label>
                    <input
                      type="text"
                      name="price"
                      value={formData.price}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="مثال: مجاني أو 100 ريال"
                    />
                  </div>

                  <div className="md:col-span-3">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaTag className="inline ml-1" />
                      الكلمات المفتاحية
                    </label>
                    <input
                      type="text"
                      name="tags"
                      value={formData.tags}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="مثال: خدمة حكومية، وثائق، استخراج"
                    />
                    <p className="text-xs text-gray-500 mt-1">افصل بين الكلمات بفاصلة</p>
                  </div>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex flex-col sm:flex-row gap-4 justify-end">
                  <button
                    type="button"
                    onClick={() => {
                      setFormData(prev => ({ ...prev, status: 'draft' }));
                      handleSubmit(null, false);
                    }}
                    disabled={loading}
                    className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 flex items-center justify-center"
                  >
                    <FaSave className="ml-2" />
                    حفظ كمسودة
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      setFormData(prev => ({ ...prev, status: 'published' }));
                      handleSubmit();
                    }}
                    disabled={loading}
                    className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 flex items-center justify-center"
                  >
                    {loading ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                    ) : (
                      <FaCheck className="ml-2" />
                    )}
                    نشر الخدمة
                  </button>
                </div>
              </div>
            </form>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="space-y-6">
              {/* Image Upload */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FaImage className="ml-2 text-primary-600" />
                  الصورة المميزة
                </h3>

                <div className="space-y-4">
                  {imagePreview ? (
                    <div className="relative group">
                      <img
                        src={imagePreview}
                        alt="معاينة الصورة"
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                        <button
                          type="button"
                          onClick={removeImage}
                          className="bg-red-500 text-white p-3 rounded-full hover:bg-red-600 transition-colors duration-200"
                        >
                          <FaTimes />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-500 transition-colors duration-200 cursor-pointer">
                      <FaImage className="text-4xl text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">اسحب الصورة هنا أو انقر للاختيار</p>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="image-upload"
                        disabled={imageUploading}
                      />
                      <label
                        htmlFor="image-upload"
                        className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer ${
                          imageUploading
                            ? 'bg-gray-400 text-white cursor-not-allowed'
                            : 'bg-primary-600 text-white hover:bg-primary-700'
                        }`}
                      >
                        {imageUploading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                            جاري الرفع...
                          </>
                        ) : (
                          <>
                            <FaUpload className="ml-2" />
                            اختر صورة
                          </>
                        )}
                      </label>
                    </div>
                  )}

                  <p className="text-xs text-gray-500">
                    الحد الأقصى: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF
                  </p>
                </div>
              </div>

              {/* PDF Upload */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FaFilePdf className="ml-2 text-red-600" />
                  ملف PDF مرفق
                </h3>

                {!formData.pdf_file ? (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200">
                    <FaFilePdf className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-600 mb-4">اختر ملف PDF لإرفاقه مع الخدمة</p>
                    <input
                      type="file"
                      accept=".pdf"
                      onChange={handlePDFUpload}
                      className="hidden"
                      id="pdf-upload"
                      disabled={pdfUploading}
                    />
                    <label
                      htmlFor="pdf-upload"
                      className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer ${
                        pdfUploading
                          ? 'bg-gray-400 text-white cursor-not-allowed'
                          : 'bg-red-600 text-white hover:bg-red-700'
                      }`}
                    >
                      {pdfUploading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                          جاري الرفع...
                        </>
                      ) : (
                        <>
                          <FaUpload className="ml-2" />
                          اختر ملف PDF
                        </>
                      )}
                    </label>
                    <p className="text-xs text-gray-500 mt-2">الحد الأقصى: 20 ميجابايت</p>
                  </div>
                ) : (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <FaFilePdf className="text-red-600 text-xl" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{formData.pdf_file.name}</p>
                          <p className="text-xs text-gray-500">
                            {(formData.pdf_file.size / 1024 / 1024).toFixed(2)} ميجابايت
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <a
                          href={formData.pdf_file.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 text-blue-600 hover:bg-blue-100 rounded-full transition-colors"
                          title="معاينة"
                        >
                          <FaEye />
                        </a>
                        <button
                          onClick={removePDF}
                          className="p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors"
                          title="حذف"
                        >
                          <FaTimes />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Settings */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FaCog className="ml-2 text-gray-600" />
                  إعدادات النشر
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <label className="flex items-center">
                      <FaStar className="text-yellow-500 ml-2" />
                      <span className="text-sm font-medium text-gray-700">خدمة مميزة</span>
                    </label>
                    <input
                      type="checkbox"
                      name="featured"
                      checked={formData.featured}
                      onChange={handleInputChange}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      حالة النشر
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="draft">مسودة</option>
                      <option value="published">منشور</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
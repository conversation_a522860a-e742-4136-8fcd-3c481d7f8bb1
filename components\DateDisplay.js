const DateDisplay = ({ dateString, className = '' }) => {
  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    const day = date.getDate();
    const year = date.getFullYear();

    // أسماء الشهور بالعربية
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const month = months[date.getMonth()];
    return `${day} ${month} ${year}`;
  };

  return <span className={className}>{formatDate(dateString)}</span>;
};

export default DateDisplay;
